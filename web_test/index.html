<!DOCTYPE html>
<html lang="vi">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>StockPal - <PERSON> ch<PERSON> k<PERSON></title>
  <link
    href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200"
    rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  <style type="text/tailwindcss">
    @theme {
      --color-clifford: #da373d;
    }
  </style>
  <link href="styles/shadcn.css" rel="stylesheet">
  <link href="styles/styles.css" rel="stylesheet">
</head>

<body>
  <div class="flex flex-1 h-screen">
    <!-- Sidebar for symbol selection -->
    <aside id="main-sidebar-wrapper" class="bg-sidebar border-r overflow-hidden">
      <div class="flex h-full w-full flex-col">
        <div data-sidebar="header" class="p-2 gap-2">
          <div
            class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF">
              <path
                d="M368.5-195q-14.81 0-26.53-8.72-11.73-8.73-17.2-22.21L237.39-450H60.77v-60h218.77l88.92 230.92 179.39-455.38q5.45-13.48 17.14-22.2 11.7-8.72 26.51-8.72 14.81 0 26.53 8.72 11.73 8.72 17.2 22.2L723.38-510H900v60H681.23l-89.69-231.31-179.39 455.38q-5.45 13.48-17.14 22.21-11.7 8.72-26.51 8.72Z" />
            </svg>
          </div>
        </div>
        <div data-sidebar="content" class="flex min-h-0 flex-1 flex-col gap-2 overflow-auto">
          <div data-sidebar="group" class="relative flex w-full min-w-0 flex-col p-2">
            <ul data-sidebar="menu" class="flex w-full min-w-0 flex-col gap-1">
              <li data-sidebar="menu-item" class="flex flex-col items-center text-center">
                <button type="button" title="Dashboard" data-sidebar="menu-button"
                  class="text-sm aspect-square size-8 p-1 cursor-pointer">
                  <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px"
                    fill="#1f1f1f">
                    <path
                      d="M530-600v-220h290v220H530ZM140-460v-360h290v360H140Zm390 320v-360h290v360H530Zm-390 0v-220h290v220H140Zm60-380h170v-240H200v240Zm390 320h170v-240H590v240Zm0-460h170v-100H590v100ZM200-200h170v-100H200v100Zm170-320Zm220-140Zm0 220ZM370-300Z" />
                  </svg>
                </button>
              </li>
              <li data-sidebar="menu-item" class="flex flex-col items-center text-center">
                <button type="button" title="Watchlist" data-sidebar="menu-button"
                  class="bg-accent text-sm aspect-square size-8 p-1 cursor-pointer">
                  <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px"
                    fill="#1f1f1f">
                    <path
                      d="M130-130v-65.77l60-60V-130h-60Zm160 0v-225.77l60-60V-130h-60Zm160 0v-285.77l60 61V-130h-60Zm160 0v-224.77l60-60V-130h-60Zm160 0v-385.77l60-60V-130h-60ZM130-351.23v-84.54l270-270 160 160 270-270v84.54l-270 270-160-160-270 270Z" />
                  </svg>
                </button>
              </li>
            </ul>
          </div>
        </div>
        <div data-sidebar="footer" class="flex flex-col gap-2 p-2">
          <ul data-sidebar="menu" class="flex w-full min-w-0 flex-col gap-1">
            <li data-sidebar="menu-item">
              <button type="button" title="Settings" data-sidebar="menu-button"
                class="text-sm aspect-square size-8 p-1 cursor-pointer">
                <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px"
                  fill="#000000">
                  <path
                    d="m387.69-100-15.23-121.85q-16.07-5.38-32.96-15.07-16.88-9.7-30.19-20.77L196.46-210l-92.3-160 97.61-73.77q-1.38-8.92-1.96-17.92-.58-9-.58-17.93 0-8.53.58-17.34t1.96-19.27L104.16-590l92.3-159.23 112.46 47.31q14.47-11.46 30.89-20.96t32.27-15.27L387.69-860h184.62l15.23 122.23q18 6.54 32.57 15.27 14.58 8.73 29.43 20.58l114-47.31L855.84-590l-99.15 74.92q2.15 9.69 2.35 18.12.19 8.42.19 16.96 0 8.15-.39 16.58-.38 8.42-2.76 19.27L854.46-370l-92.31 160-112.61-48.08q-14.85 11.85-30.31 20.96-15.46 9.12-31.69 14.89L572.31-100H387.69ZM440-160h78.62L533-267.15q30.62-8 55.96-22.73 25.35-14.74 48.89-37.89L737.23-286l39.39-68-86.77-65.38q5-15.54 6.8-30.47 1.81-14.92 1.81-30.15 0-15.62-1.81-30.15-1.8-14.54-6.8-29.7L777.38-606 738-674l-100.54 42.38q-20.08-21.46-48.11-37.92-28.04-16.46-56.73-23.31L520-800h-79.38l-13.24 106.77q-30.61 7.23-56.53 22.15-25.93 14.93-49.47 38.46L222-674l-39.38 68L269-541.62q-5 14.24-7 29.62t-2 32.38q0 15.62 2 30.62 2 15 6.62 29.62l-86 65.38L222-286l99-42q22.77 23.38 48.69 38.31 25.93 14.92 57.31 22.92L440-160Zm40.46-200q49.92 0 84.96-35.04 35.04-35.04 35.04-84.96 0-49.92-35.04-84.96Q530.38-600 480.46-600q-50.54 0-85.27 35.04T360.46-480q0 49.92 34.73 84.96Q429.92-360 480.46-360ZM480-480Z" />
                </svg>
              </button>
            </li>
          </ul>
        </div>
      </div>
    </aside>


    <div class="flex flex-col w-full overflow-hidden">
      <!-- Topbar -->
      <div id="main-topbar-wrapper" class="bg-sidebar border-b">
        <!-- Tabbar for selected stocks -->
        <div
          class="tabs-container overflow-x-auto bg-background overscroll-contain scrollbar-thin scrollbar-track-rounded">
          <div id="selectedSymbolsPlaceholder" role="tablist" class="flex h-10 items-center">
          </div>
        </div>
      </div>

      <main id="main-contain-wrapper" class="flex-1 p-4 overflow-auto">
        <div id="watchlistView" class="hidden">
          <!-- Watchlist content will be dynamically inserted here -->
        </div>

        <div id="stockDetailView" class="hidden">
          <div id="analysisSummary" class="mb-6"></div>
          <div class="chart-container mb-8">
            <div id="chart-price-history"></div>
          </div>
          <div id="allIndicators" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-1"></div>
        </div>
      </main>
    </div>
  </div>

  <!-- Dialog for creating/editing a watchlist with shadcn-ui styling -->
  <div id="createWatchlistDialog"
    class="fixed inset-0 z-50 bg-background/80 backdrop-blur-xs data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 flex items-center justify-center hidden">
    <div
      class="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg">
      <div class="flex flex-col space-y-1.5 text-center sm:text-left">
        <h3 id="watchlistDialogTitle" class="text-lg font-semibold leading-none tracking-tight">Tạo danh sách theo dõi
        </h3>
        </h3>
        <p class="text-sm text-muted-foreground">Nhập tên và chọn mã cổ phiếu cho danh sách theo dõi của bạn</p>
      </div>
      <div class="grid gap-4 py-4">
        <div class="grid gap-2">
          <label for="newWatchlistNameInput"
            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Tên danh
            sách theo dõi</label>
          <div class="relative">
            <input id="newWatchlistNameInput"
              class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="To the moon">
            <div id="watchlistNameError" class="text-sm  text-red-600 text-destructive mt-1 hidden">Tên này
              đã tồn tại</div>
          </div>
        </div>

        <div class="grid gap-2">
          <label for="symbolSearchInput" class="text-sm font-medium leading-none">Tìm mã cổ phiếu</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-search h-4 w-4 opacity-50">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.3-4.3"></path>
              </svg>
            </div>
            <input type="text" id="symbolSearchInput"
              placeholder="Nhập mã cổ phiếu cần thêm vào danh sách. (ví dụ: HPG)"
              class="flex h-10 w-full rounded-md border border-input bg-background pl-10 pr-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
            <div id="symbolSearchAutocomplete"
              class="absolute z-50 hidden w-full min-w-[8rem] overflow-hidden rounded-md border bg-background p-1 text-foreground shadow-md">
              <!-- Autocomplete options will be rendered here -->
            </div>
          </div>
        </div>

        <div class="grid gap-2">
          <label for="watchlistSymbolsSelect"
            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Mã
            cổ phiếu đã chọn</label>
          <div class="relative">
            <div class="flex flex-wrap gap-1 p-2 border rounded-md min-h-[100px] max-h-[200px] overflow-y-auto">
              <div id="selectedSymbolsContainer" class="flex flex-wrap gap-1 w-full">
                <!-- Selected symbols will be rendered here -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex justify-end gap-2">
        <button type="button" id="cancelCreateWatchlistBtn"
          class="whitespace-nowrap rounded-md text-sm font-medium transition-colors border border-input bg-background text-accent-foreground h-10 px-4 py-2 cursor-pointer">
          Hủy
        </button>
        <button type="button" id="confirmCreateWatchlistBtn"
          class="whitespace-nowrap rounded-md text-sm font-medium transition-colors border border-input bg-primary text-primary-foreground h-10 px-4 py-2 cursor-pointer">
          Tạo
        </button>
      </div>
    </div>
  </div>

  <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script type="module" src="scripts/script.js"></script>
</body>

</html>
let watchlistViewEl = null;
let createWatchlistDialogEl = null;
let newWatchlistNameInputEl = null;
let confirmCreateWatchlistBtnEl = null;
let cancelCreateWatchlistBtnEl = null;
let searchAutocompleteEl = null;
let watchlistDialogTitleEl = null;
let watchlistNameErrorEl = null;
let symbolSearchInputEl = null;
let symbolSearchAutocompleteEl = null;
let selectedSymbolsContainerEl = null;

let watchlists = {}; // Format: { "WatchlistName1": ["SYM1", "SYM2"], "WatchlistName2": ["SYM3"] }
let selectedWatchlists = []; // Array of selected watchlist IDs
let currentSelectedWatchlistId = null; // The name of the currently selected watchlist
let isEditMode = false; // Whether we're editing an existing watchlist or creating a new one
let editingWatchlistId = null; // The ID of the watchlist being edited
let dialogSelectedSymbols = []; // Symbols selected in the dialog
const WATCHLIST_STORAGE_KEY = 'stockpalWatchlists';

// Sample stock data - in a real app, this would come from an API
const SAMPLE_STOCKS = [
    { symbol: 'HPG', name: 'Hòa Phát Group', price: '21.55', change: '+0.55', changePercent: '*****%', trend: 'up' },
    { symbol: 'VNM', name: 'Vinamilk', price: '71.20', change: '-0.30', changePercent: '-0.42%', trend: 'down' },
    { symbol: 'TCB', name: 'Techcombank', price: '32.45', change: '+0.75', changePercent: '*****%', trend: 'up' },
    { symbol: 'VCB', name: 'Vietcombank', price: '89.90', change: '*****', changePercent: '*****%', trend: 'up' },
    { symbol: 'VIC', name: 'Vingroup', price: '43.25', change: '-0.65', changePercent: '-1.48%', trend: 'down' },
    { symbol: 'FPT', name: 'FPT Corp', price: '112.80', change: '*****', changePercent: '*****%', trend: 'up' },
    { symbol: 'MWG', name: 'Mobile World', price: '53.10', change: '-0.90', changePercent: '-1.67%', trend: 'down' },
    { symbol: 'VHM', name: 'Vinhomes', price: '47.35', change: '+0.25', changePercent: '+0.53%', trend: 'up' },
    { symbol: 'MSN', name: 'Masan Group', price: '74.60', change: '-1.40', changePercent: '-1.84%', trend: 'down' },
    { symbol: 'VRE', name: 'Vincom Retail', price: '25.75', change: '+0.45', changePercent: '*****%', trend: 'up' }
];

function loadWatchlists() {
    const storedWatchlists = localStorage.getItem(WATCHLIST_STORAGE_KEY);
    if (storedWatchlists) {
        watchlists = JSON.parse(storedWatchlists);
        const availableWatchlistIds = Object.keys(watchlists);
        if (availableWatchlistIds.length > 0) {
            // Try to keep currentSelectedWatchlistId if it's still valid, else default
            if (currentSelectedWatchlistId && watchlists.hasOwnProperty(currentSelectedWatchlistId)) {
                // It's still valid, do nothing
            } else {
                currentSelectedWatchlistId = availableWatchlistIds[0];
            }
        } else {
            currentSelectedWatchlistId = null;
        }
    } else {
        watchlists = {}; // Start empty if nothing in storage
        currentSelectedWatchlistId = null;
    }
}

function saveWatchlists() {
    localStorage.setItem(WATCHLIST_STORAGE_KEY, JSON.stringify(watchlists));
}

export function initWatchlist(element) {
    watchlistViewEl = element;
    loadWatchlists(); // Load watchlists from storage

    // Get dialog elements once during initialization
    createWatchlistDialogEl = document.getElementById('createWatchlistDialog');
    newWatchlistNameInputEl = document.getElementById('newWatchlistNameInput');
    confirmCreateWatchlistBtnEl = document.getElementById('confirmCreateWatchlistBtn');
    cancelCreateWatchlistBtnEl = document.getElementById('cancelCreateWatchlistBtn');
    watchlistDialogTitleEl = document.getElementById('watchlistDialogTitle');
    watchlistNameErrorEl = document.getElementById('watchlistNameError');
    symbolSearchInputEl = document.getElementById('symbolSearchInput');
    symbolSearchAutocompleteEl = document.getElementById('symbolSearchAutocomplete');
    selectedSymbolsContainerEl = document.getElementById('selectedSymbolsContainer');

    if (confirmCreateWatchlistBtnEl) {
        confirmCreateWatchlistBtnEl.addEventListener('click', handleWatchlistSave);
    }
    if (cancelCreateWatchlistBtnEl) {
        cancelCreateWatchlistBtnEl.addEventListener('click', closeCreateWatchlistDialog);
    }

    // Initialize symbol search in dialog
    if (symbolSearchInputEl && symbolSearchAutocompleteEl) {
        initDialogSymbolSearch();
    }

    // Add input event to validate watchlist name
    if (newWatchlistNameInputEl && watchlistNameErrorEl) {
        newWatchlistNameInputEl.addEventListener('input', validateWatchlistName);
    }
}

function openCreateWatchlistDialog() {
    // Reset dialog state
    isEditMode = false;
    editingWatchlistId = null;
    dialogSelectedSymbols = [];

    // Update UI
    if (watchlistNameErrorEl) {
        watchlistNameErrorEl.classList.add('hidden');
    }
    if (selectedSymbolsContainerEl) {
        selectedSymbolsContainerEl.innerHTML = '';
    }

    // Show dialog
    if (createWatchlistDialogEl) {
        createWatchlistDialogEl.style.display = 'flex';
        createWatchlistDialogEl.setAttribute('data-state', 'open');
    }
    if (newWatchlistNameInputEl) {
        newWatchlistNameInputEl.value = '';
        newWatchlistNameInputEl.focus();
    }
}

function openEditWatchlistDialog(watchlistId) {
    if (!watchlists[watchlistId]) return;

    // Set dialog state
    isEditMode = true;
    editingWatchlistId = watchlistId;
    dialogSelectedSymbols = [...watchlists[watchlistId]]; // Copy symbols from the watchlist

    // Update UI
    if (watchlistDialogTitleEl) {
        watchlistDialogTitleEl.textContent = 'Edit Watchlist';
    }
    if (confirmCreateWatchlistBtnEl) {
        confirmCreateWatchlistBtnEl.textContent = 'Save Changes';
    }
    if (watchlistNameErrorEl) {
        watchlistNameErrorEl.classList.add('hidden');
    }
    if (newWatchlistNameInputEl) {
        newWatchlistNameInputEl.value = watchlistId;
    }

    // Render selected symbols
    renderDialogSelectedSymbols();

    // Show dialog
    if (createWatchlistDialogEl) {
        createWatchlistDialogEl.style.display = 'flex';
        createWatchlistDialogEl.setAttribute('data-state', 'open');
    }
}

function closeCreateWatchlistDialog() {
    if (createWatchlistDialogEl) {
        createWatchlistDialogEl.setAttribute('data-state', 'closed');
        // Use a small timeout to allow the animation to play
        setTimeout(() => {
            createWatchlistDialogEl.style.display = 'none';
        }, 200);
    }

    // Reset dialog state
    isEditMode = false;
    editingWatchlistId = null;
    dialogSelectedSymbols = [];

    // Clear inputs
    if (newWatchlistNameInputEl) newWatchlistNameInputEl.value = '';
    if (symbolSearchInputEl) symbolSearchInputEl.value = '';
    if (watchlistNameErrorEl) watchlistNameErrorEl.classList.add('hidden');
    if (selectedSymbolsContainerEl) selectedSymbolsContainerEl.innerHTML = '';
}

export function displayWatchlist() {
    if (watchlistViewEl) {
        watchlistViewEl.style.display = 'block';
        loadWatchlists(); // Ensure we have the latest from storage, e.g. if modified in another tab (though unlikely for this app)
        renderWatchlistInterface(); // This will set up everything including the table
    }
}

export function hideWatchlist() {
    if (watchlistViewEl) {
        watchlistViewEl.style.display = 'none';
    }
}

function validateWatchlistName() {
    if (!newWatchlistNameInputEl || !watchlistNameErrorEl) return true;

    const name = newWatchlistNameInputEl.value.trim();

    // Check if name is empty
    if (!name) {
        watchlistNameErrorEl.textContent = 'Watchlist name cannot be empty';
        watchlistNameErrorEl.classList.remove('hidden');
        return false;
    }

    // In edit mode, if the name hasn't changed, it's valid
    if (isEditMode && name === editingWatchlistId) {
        watchlistNameErrorEl.classList.add('hidden');
        return true;
    }

    // Check if name already exists
    if (watchlists.hasOwnProperty(name)) {
        watchlistNameErrorEl.textContent = 'A watchlist with this name already exists';
        watchlistNameErrorEl.classList.remove('hidden');
        return false;
    }

    // Name is valid
    watchlistNameErrorEl.classList.add('hidden');
    return true;
}

function handleWatchlistSave() {
    if (!newWatchlistNameInputEl) return;

    // Validate watchlist name
    if (!validateWatchlistName()) {
        newWatchlistNameInputEl.focus();
        return;
    }

    const newName = newWatchlistNameInputEl.value.trim();

    if (isEditMode) {
        // Handle edit mode
        if (editingWatchlistId && editingWatchlistId !== newName) {
            // Name has changed, create a new watchlist with the new name
            watchlists[newName] = dialogSelectedSymbols;
            delete watchlists[editingWatchlistId];

            // Update selected watchlists if the edited one was selected
            const index = selectedWatchlists.indexOf(editingWatchlistId);
            if (index !== -1) {
                selectedWatchlists[index] = newName;
            }
        } else {
            // Just update the symbols
            watchlists[editingWatchlistId] = dialogSelectedSymbols;
        }
    } else {
        // Handle create mode
        watchlists[newName] = dialogSelectedSymbols;
        selectedWatchlists.push(newName); // Add to selected watchlists
    }

    // Save and update UI
    saveWatchlists();
    initWatchlistMultiSelect();
    renderWatchlistTable();

    // Close dialog
    closeCreateWatchlistDialog();
}

function initWatchlistMultiSelect() {
    const watchlistDropdown = document.getElementById('watchlist-dropdown');
    if (!watchlistDropdown) return;

    const watchlistIds = Object.keys(watchlists);

    // Clear dropdown content
    watchlistDropdown.innerHTML = '';

    if (watchlistIds.length === 0) {
        const emptyItem = document.createElement('div');
        emptyItem.className = 'relative flex cursor-default select-none items-center rounded-sm py-1.5 px-2 text-sm outline-none text-muted-foreground';
        emptyItem.textContent = 'No watchlists available';
        watchlistDropdown.appendChild(emptyItem);
    } else {
        watchlistIds.forEach(id => {
            const item = document.createElement('div');
            item.className = 'relative flex justify-between cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground';
            item.dataset.value = id;

            // Left side with checkbox and text
            const leftSide = document.createElement('div');
            leftSide.className = 'flex items-center flex-1';

            // Create checkbox indicator
            const checkSpan = document.createElement('span');
            checkSpan.className = 'absolute left-2 flex h-3.5 w-3.5 items-center justify-center';

            if (selectedWatchlists.includes(id)) {
                const checkIcon = document.createElement('svg');
                checkIcon.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
                checkIcon.setAttribute('width', '16');
                checkIcon.setAttribute('height', '16');
                checkIcon.setAttribute('viewBox', '0 0 24 24');
                checkIcon.setAttribute('fill', 'none');
                checkIcon.setAttribute('stroke', 'currentColor');
                checkIcon.setAttribute('stroke-width', '2');
                checkIcon.setAttribute('stroke-linecap', 'round');
                checkIcon.setAttribute('stroke-linejoin', 'round');
                checkIcon.className = 'h-4 w-4';
                checkIcon.innerHTML = '<polyline points="20 6 9 17 4 12"></polyline>';
                checkSpan.appendChild(checkIcon);
            }

            // Add text
            const textSpan = document.createElement('span');
            textSpan.textContent = id;

            leftSide.appendChild(textSpan);
            item.appendChild(checkSpan);
            item.appendChild(leftSide);

            // Right side with edit button
            const editButton = document.createElement('button');
            editButton.className = 'ml-2 text-muted-foreground hover:text-foreground';
            editButton.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pencil">
                    <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                    <path d="m15 5 4 4"></path>
                </svg>
            `;

            // Add click handlers
            leftSide.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleWatchlistSelection(id);
            });

            editButton.addEventListener('click', (e) => {
                e.stopPropagation();
                openEditWatchlistDialog(id);
            });

            item.appendChild(editButton);
            watchlistDropdown.appendChild(item);
        });
    }

    // Update the selected watchlists display
    updateSelectedWatchlistsDisplay();
}

function toggleWatchlistSelection(watchlistId) {
    const index = selectedWatchlists.indexOf(watchlistId);

    if (index === -1) {
        // Add to selection
        selectedWatchlists.push(watchlistId);
    } else {
        // Remove from selection
        selectedWatchlists.splice(index, 1);
    }

    // Update the display
    updateSelectedWatchlistsDisplay();

    // Re-render the dropdown to update checkmarks
    initWatchlistMultiSelect();

    // Re-render the table with filtered data
    renderWatchlistTable();
}

function updateSelectedWatchlistsDisplay() {
    const selectedContainer = document.getElementById('selected-watchlists');
    const placeholder = document.getElementById('watchlist-placeholder');

    if (!selectedContainer) return;

    // Clear previous badges except placeholder
    Array.from(selectedContainer.children).forEach(child => {
        if (child.id !== 'watchlist-placeholder') {
            child.remove();
        }
    });

    // Show/hide placeholder based on selection
    if (selectedWatchlists.length > 0) {
        placeholder?.classList.add('hidden');

        // If more than 2 watchlists are selected, show first one + count
        if (selectedWatchlists.length > 2) {
            // Add the first watchlist
            const firstId = selectedWatchlists[0];
            const firstBadge = document.createElement('span');
            firstBadge.className = 'inline-flex items-center rounded-md bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground';

            const firstText = document.createElement('span');
            firstText.textContent = firstId;
            firstBadge.appendChild(firstText);

            const firstCloseBtn = document.createElement('button');
            firstCloseBtn.className = 'ml-1 rounded-full text-secondary-foreground hover:bg-secondary-foreground/20';
            firstCloseBtn.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x">
                    <path d="M18 6 6 18"></path>
                    <path d="m6 6 12 12"></path>
                </svg>
            `;
            firstCloseBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleWatchlistSelection(firstId);
            });

            firstBadge.appendChild(firstCloseBtn);
            selectedContainer.appendChild(firstBadge);

            // Add the count badge
            const countBadge = document.createElement('span');
            countBadge.className = 'inline-flex items-center rounded-md bg-primary px-2 py-1 text-xs font-medium text-primary-foreground';
            countBadge.textContent = `+${selectedWatchlists.length - 1}`;
            selectedContainer.appendChild(countBadge);
        } else {
            // Add badges for all selected watchlists (1 or 2)
            selectedWatchlists.forEach(id => {
                const badge = document.createElement('span');
                badge.className = 'inline-flex items-center rounded-md bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground';

                const text = document.createElement('span');
                text.textContent = id;
                badge.appendChild(text);

                const closeBtn = document.createElement('button');
                closeBtn.className = 'ml-1 rounded-full text-secondary-foreground hover:bg-secondary-foreground/20';
                closeBtn.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                `;
                closeBtn.addEventListener('click', (e) => {
                    e.stopPropagation(); // Prevent dropdown from toggling
                    toggleWatchlistSelection(id);
                });

                badge.appendChild(closeBtn);
                selectedContainer.appendChild(badge);
            });
        }
    } else {
        placeholder?.classList.remove('hidden');
    }
}

function toggleWatchlistDropdown() {
    const dropdown = document.getElementById('watchlist-dropdown');
    if (dropdown) {
        dropdown.classList.toggle('hidden');
    }
}

function initSearchAutocomplete() {
    const searchInput = document.getElementById('watchlist-search');
    const autocompleteDropdown = document.getElementById('search-autocomplete');

    if (!searchInput || !autocompleteDropdown) return;

    // Sample stock symbols for autocomplete
    const stockSymbols = SAMPLE_STOCKS.map(stock => stock.symbol);

    // Add input event listener
    searchInput.addEventListener('input', function () {
        const query = this.value.trim().toUpperCase();

        if (query.length === 0) {
            autocompleteDropdown.classList.add('hidden');
            return;
        }

        // Filter symbols based on input
        const filteredSymbols = stockSymbols.filter(symbol =>
            symbol.includes(query)
        );

        // Clear previous results
        autocompleteDropdown.innerHTML = '';

        if (filteredSymbols.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'py-6 text-center text-sm text-muted-foreground';
            noResults.textContent = 'No results found';
            autocompleteDropdown.appendChild(noResults);
        } else {
            filteredSymbols.forEach(symbol => {
                const item = document.createElement('div');
                item.className = 'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground';

                // Check if symbol is already in any selected watchlist
                const isSymbolInWatchlists = isSymbolInSelectedWatchlists(symbol);

                // Create checkbox indicator for selected symbols
                const checkSpan = document.createElement('span');
                checkSpan.className = 'absolute left-2 flex h-3.5 w-3.5 items-center justify-center';

                if (isSymbolInWatchlists) {
                    const checkIcon = document.createElement('svg');
                    checkIcon.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
                    checkIcon.setAttribute('width', '16');
                    checkIcon.setAttribute('height', '16');
                    checkIcon.setAttribute('viewBox', '0 0 24 24');
                    checkIcon.setAttribute('fill', 'none');
                    checkIcon.setAttribute('stroke', 'currentColor');
                    checkIcon.setAttribute('stroke-width', '2');
                    checkIcon.setAttribute('stroke-linecap', 'round');
                    checkIcon.setAttribute('stroke-linejoin', 'round');
                    checkIcon.className = 'h-4 w-4';
                    checkIcon.innerHTML = '<polyline points="20 6 9 17 4 12"></polyline>';
                    checkSpan.appendChild(checkIcon);
                }

                item.appendChild(checkSpan);

                // Add text
                const textSpan = document.createElement('span');
                textSpan.textContent = symbol;
                item.appendChild(textSpan);

                item.addEventListener('click', () => {
                    searchInput.value = symbol;
                    autocompleteDropdown.classList.add('hidden');
                    addSymbolToSelectedWatchlists(symbol);
                });

                autocompleteDropdown.appendChild(item);
            });
        }

        // Show dropdown
        autocompleteDropdown.classList.remove('hidden');
    });

    // Show dropdown on focus if input has value
    searchInput.addEventListener('focus', function () {
        if (this.value.trim().length > 0) {
            const event = new Event('input');
            this.dispatchEvent(event);
        }
    });

    // Add keydown event for keyboard navigation
    searchInput.addEventListener('keydown', function (e) {
        if (e.key === 'Enter' && this.value.trim().length > 0) {
            e.preventDefault();
            addSymbolToSelectedWatchlists(this.value.trim().toUpperCase());
            this.value = '';
            autocompleteDropdown.classList.add('hidden');
        }
    });
}

// Helper function to check if a symbol is in any selected watchlist
function isSymbolInSelectedWatchlists(symbol) {
    if (selectedWatchlists.length === 0) return false;

    for (const watchlistId of selectedWatchlists) {
        if (watchlists[watchlistId] && watchlists[watchlistId].includes(symbol)) {
            return true;
        }
    }

    return false;
}

function addSymbolToSelectedWatchlists(symbol) {
    if (selectedWatchlists.length === 0) {
        alert('Please select at least one watchlist first.');
        return;
    }

    if (!/^[A-Z0-9]{1,10}$/.test(symbol)) {
        alert("Invalid symbol format. Please use 1-10 uppercase letters and numbers (e.g., AAPL, HPG).");
        return;
    }

    let addedToAny = false;

    selectedWatchlists.forEach(watchlistId => {
        if (!watchlists[watchlistId].includes(symbol)) {
            watchlists[watchlistId].push(symbol);
            watchlists[watchlistId].sort(); // Keep symbols sorted
            addedToAny = true;
        }
    });

    if (addedToAny) {
        saveWatchlists();
        renderWatchlistTable();
    } else {
        alert(`Symbol ${symbol} is already in all selected watchlists.`);
    }
}

function renderWatchlistTable() {
    const tableContainer = document.getElementById('watchlist-table-container');
    if (!tableContainer) return;

    // Get all symbols from selected watchlists
    let allSymbols = new Set();

    if (selectedWatchlists.length === 0) {
        tableContainer.innerHTML = `
            <div class="rounded-md bg-muted p-4 text-sm text-muted-foreground">
                <p>Select at least one watchlist to view stocks, or create a new one.</p>
            </div>
        `;
        return;
    }

    // Collect all symbols from selected watchlists
    selectedWatchlists.forEach(watchlistId => {
        if (watchlists[watchlistId]) {
            watchlists[watchlistId].forEach(symbol => allSymbols.add(symbol));
        }
    });

    if (allSymbols.size === 0) {
        tableContainer.innerHTML = `
            <div class="rounded-md bg-muted p-4 text-sm text-muted-foreground">
                <p>No symbols in the selected watchlists. Add symbols using the search box above.</p>
            </div>
        `;
        return;
    }

    // Get stock data for the symbols
    const stocksToDisplay = [];
    allSymbols.forEach(symbol => {
        // Find the stock in our sample data
        const stockData = SAMPLE_STOCKS.find(stock => stock.symbol === symbol);
        if (stockData) {
            stocksToDisplay.push(stockData);
        } else {
            // If not found in sample data, create a basic entry
            stocksToDisplay.push({
                symbol: symbol,
                name: symbol,
                price: 'N/A',
                change: 'N/A',
                changePercent: 'N/A',
                trend: 'neutral'
            });
        }
    });

    // Create the table with shadcn-ui styling
    let tableHTML = `
        <div class="rounded-md border">
            <div class="relative w-full overflow-auto">
                <table class="w-full caption-bottom text-sm">
                    <thead class="[&_tr]:border-b">
                        <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                            <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Symbol</th>
                            <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Name</th>
                            <th class="h-12 px-4 text-right align-middle font-medium text-muted-foreground">Price</th>
                            <th class="h-12 px-4 text-right align-middle font-medium text-muted-foreground">Change</th>
                            <th class="h-12 px-4 text-center align-middle font-medium text-muted-foreground">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="[&_tr:last-child]:border-0">
    `;

    stocksToDisplay.forEach(stock => {
        let trendClass = '';
        if (stock.trend === 'up') {
            trendClass = 'text-green-600';
        } else if (stock.trend === 'down') {
            trendClass = 'text-red-600';
        }

        tableHTML += `
            <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                <td class="p-4 align-middle font-medium">${stock.symbol}</td>
                <td class="p-4 align-middle">${stock.name}</td>
                <td class="p-4 align-middle text-right">${stock.price}</td>
                <td class="p-4 align-middle text-right ${trendClass}">
                    ${stock.change} (${stock.changePercent})
                </td>
                <td class="p-4 align-middle text-center">
                    <button class="remove-symbol-btn inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium h-9 px-3 bg-destructive text-destructive-foreground hover:bg-destructive/90" data-symbol="${stock.symbol}">
                        Remove
                    </button>
                </td>
            </tr>
        `;
    });

    tableHTML += `
                    </tbody>
                </table>
            </div>
        </div>
    `;

    tableContainer.innerHTML = tableHTML;

    // Add event listeners to new "Remove" buttons
    tableContainer.querySelectorAll('.remove-symbol-btn').forEach(button => {
        button.addEventListener('click', function () {
            const symbolToRemove = this.dataset.symbol;
            handleRemoveSymbolFromWatchlist(symbolToRemove);
        });
    });
}

function initDialogSymbolSearch() {
    if (!symbolSearchInputEl || !symbolSearchAutocompleteEl) return;

    // Sample stock symbols for autocomplete
    const stockSymbols = SAMPLE_STOCKS.map(stock => stock.symbol);

    // Add input event listener
    symbolSearchInputEl.addEventListener('input', function () {
        const query = this.value.trim().toUpperCase();

        if (query.length === 0) {
            symbolSearchAutocompleteEl.classList.add('hidden');
            return;
        }

        // Filter symbols based on input
        const filteredSymbols = stockSymbols.filter(symbol =>
            symbol.includes(query)
        );

        // Clear previous results
        symbolSearchAutocompleteEl.innerHTML = '';

        if (filteredSymbols.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'py-6 text-center text-sm text-muted-foreground';
            noResults.textContent = 'No results found';
            symbolSearchAutocompleteEl.appendChild(noResults);
        } else {
            filteredSymbols.forEach(symbol => {
                const item = document.createElement('div');
                item.className = 'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground';

                // Check if symbol is already selected in the dialog
                const isSymbolSelected = dialogSelectedSymbols.includes(symbol);

                // Create checkbox indicator for selected symbols
                const checkSpan = document.createElement('span');
                checkSpan.className = 'absolute left-2 flex h-3.5 w-3.5 items-center justify-center';

                if (isSymbolSelected) {
                    const checkIcon = document.createElement('svg');
                    checkIcon.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
                    checkIcon.setAttribute('width', '16');
                    checkIcon.setAttribute('height', '16');
                    checkIcon.setAttribute('viewBox', '0 0 24 24');
                    checkIcon.setAttribute('fill', 'none');
                    checkIcon.setAttribute('stroke', 'currentColor');
                    checkIcon.setAttribute('stroke-width', '2');
                    checkIcon.setAttribute('stroke-linecap', 'round');
                    checkIcon.setAttribute('stroke-linejoin', 'round');
                    checkIcon.className = 'h-4 w-4';
                    checkIcon.innerHTML = '<polyline points="20 6 9 17 4 12"></polyline>';
                    checkSpan.appendChild(checkIcon);
                }

                item.appendChild(checkSpan);

                // Add text
                const textSpan = document.createElement('span');
                textSpan.textContent = symbol;
                item.appendChild(textSpan);

                item.addEventListener('click', () => {
                    toggleDialogSymbolSelection(symbol);
                    symbolSearchInputEl.value = '';
                    symbolSearchAutocompleteEl.classList.add('hidden');
                });

                symbolSearchAutocompleteEl.appendChild(item);
            });
        }

        // Show dropdown
        symbolSearchAutocompleteEl.classList.remove('hidden');
    });

    // Show dropdown on focus if input has value
    symbolSearchInputEl.addEventListener('focus', function () {
        if (this.value.trim().length > 0) {
            const event = new Event('input');
            this.dispatchEvent(event);
        }
    });

    // Add keydown event for keyboard navigation
    symbolSearchInputEl.addEventListener('keydown', function (e) {
        if (e.key === 'Enter' && this.value.trim().length > 0) {
            e.preventDefault();
            const symbol = this.value.trim().toUpperCase();
            toggleDialogSymbolSelection(symbol);
            this.value = '';
            symbolSearchAutocompleteEl.classList.add('hidden');
        }
    });
}

function toggleDialogSymbolSelection(symbol) {
    if (!symbol) return;

    const index = dialogSelectedSymbols.indexOf(symbol);

    if (index === -1) {
        // Add to selection
        dialogSelectedSymbols.push(symbol);
    } else {
        // Remove from selection
        dialogSelectedSymbols.splice(index, 1);
    }

    // Update the display
    renderDialogSelectedSymbols();
}

function renderDialogSelectedSymbols() {
    if (!selectedSymbolsContainerEl) return;

    // Clear previous badges
    selectedSymbolsContainerEl.innerHTML = '';

    if (dialogSelectedSymbols.length === 0) {
        const emptyMessage = document.createElement('span');
        emptyMessage.className = 'text-muted-foreground text-sm';
        emptyMessage.textContent = 'No symbols selected. Use the search box below to add symbols.';
        selectedSymbolsContainerEl.appendChild(emptyMessage);
        return;
    }

    // Sort symbols alphabetically
    const sortedSymbols = [...dialogSelectedSymbols].sort();

    // Add badges for selected symbols
    sortedSymbols.forEach(symbol => {
        const badge = document.createElement('span');
        badge.className = 'inline-flex items-center rounded-md bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground';

        const text = document.createElement('span');
        text.textContent = symbol;
        badge.appendChild(text);

        const closeBtn = document.createElement('button');
        closeBtn.className = 'ml-1 rounded-full text-secondary-foreground hover:bg-secondary-foreground/20';
        closeBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
            </svg>
        `;
        closeBtn.addEventListener('click', () => {
            toggleDialogSymbolSelection(symbol);
        });

        badge.appendChild(closeBtn);
        selectedSymbolsContainerEl.appendChild(badge);
    });
}

function handleRemoveSymbolFromWatchlist(symbolToRemove) {
    if (selectedWatchlists.length === 0) return;

    // Remove the symbol from all selected watchlists
    selectedWatchlists.forEach(watchlistId => {
        if (watchlists[watchlistId]) {
            watchlists[watchlistId] = watchlists[watchlistId].filter(s => s !== symbolToRemove);
        }
    });

    saveWatchlists();
    renderWatchlistTable(); // Re-render the table
}

function renderWatchlistInterface() {
    if (!watchlistViewEl) return;

    // Clear previous content and set up watchlist UI with shadcn-ui styling
    watchlistViewEl.innerHTML = `
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold">Danh sách theo dõi</h2>
            <div class="flex gap-2">
                <button id="view-watchlists-btn" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-3">
                    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="M180-100v-530.77q0-29.92 21.19-51.11 21.2-21.2 51.12-21.2h301.15q29.92 0 51.12 21.2 21.19 21.19 21.19 51.11V-100L403.08-213.08 180-100Zm60-92.54 163.08-86.38 162.69 86.38v-438.23q0-5.38-3.46-8.85-3.46-3.46-8.85-3.46H252.31q-5.39 0-8.85 3.46-3.46 3.47-3.46 8.85v438.23Zm480-58.61v-536.54q0-5.39-3.46-8.85t-8.85-3.46H295.77v-60h411.92q29.92 0 51.12 21.19Q780-817.61 780-787.69v536.54h-60ZM240-643.08h325.77H240Z"/></svg>
                    Danh sách
                </button>
                <button id="create-watchlist-btn" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium h-9 px-3 bg-secondary text-secondary-foreground hover:bg-secondary/90 disabled:pointer-events-none disabled:opacity-50">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus">
                        <path d="M12 5v14M5 12h14"></path>
                    </svg>
                    Tạo mới
                </button>
            </div>
        </div>

        <div class="mb-6">
            <div class="flex gap-4">
                <!-- Multi-select dropdown for watchlists -->
                <div class="relative">
                    <div id="watchlist-multi-select" class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm">
                        <div id="selected-watchlists" class="flex flex-wrap gap-1 overflow-hidden">
                            <!-- Selected watchlists will be rendered here -->
                            <span class="text-muted-foreground" id="watchlist-placeholder">Watchlists...</span>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-4 w-4 opacity-50">
                            <path d="m6 9 6 6 6-6"></path>
                        </svg>
                    </div>
                    <div id="watchlist-dropdown" class="absolute z-50 hidden w-full min-w-[8rem] overflow-hidden rounded-md border bg-background p-1 text-foreground shadow-md">
                        <!-- Watchlist options will be rendered here -->
                    </div>
                </div>

                <!-- Search input with autocomplete -->
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search h-4 w-4 opacity-50">
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="m21 21-4.3-4.3"></path>
                        </svg>
                    </div>
                    <input type="text" id="watchlist-search" placeholder="Search for symbols (e.g. HPG)"
                        class="flex h-10 w-full rounded-md border border-input bg-background pl-10 pr-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
                    <div id="search-autocomplete" class="absolute z-50 hidden w-full min-w-[8rem] overflow-hidden rounded-md border bg-background p-1 text-foreground shadow-md">
                        <!-- Autocomplete options will be rendered here -->
                    </div>
                </div>
            </div>
        </div>

        <div id="watchlist-table-container" class="overflow-x-auto">
            <!-- Table will be rendered here by renderWatchlistTable -->
        </div>
    `;

    // Initialize the multi-select dropdown
    initWatchlistMultiSelect();

    // Initialize search autocomplete
    initSearchAutocomplete();

    // Render the table with stock data
    renderWatchlistTable();

    // Add event listeners for controls
    const createBtn = document.getElementById('create-watchlist-btn');
    if (createBtn) {
        createBtn.addEventListener('click', openCreateWatchlistDialog);
    }

    // Toggle watchlist dropdown when clicking on the select
    const watchlistSelect = document.getElementById('watchlist-multi-select');
    if (watchlistSelect) {
        watchlistSelect.addEventListener('click', toggleWatchlistDropdown);
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function (event) {
        const watchlistDropdown = document.getElementById('watchlist-dropdown');
        const watchlistSelect = document.getElementById('watchlist-multi-select');
        const searchAutocomplete = document.getElementById('search-autocomplete');
        const searchInput = document.getElementById('watchlist-search');

        if (watchlistDropdown && watchlistSelect &&
            !watchlistSelect.contains(event.target) &&
            !watchlistDropdown.contains(event.target)) {
            watchlistDropdown.classList.add('hidden');
        }

        if (searchAutocomplete && searchInput &&
            !searchInput.contains(event.target) &&
            !searchAutocomplete.contains(event.target)) {
            searchAutocomplete.classList.add('hidden');
        }
    });
}